export interface Domain {
  id: string
  organization_id: string
  frontend_hostname?: string
  backend_hostname?: string
  data?: {
    branding?: {
      name?: string
      slogan?: string
      logo_url?: string
      favicon_url?: string
    }
    theme?: {
      colors?: Record<string, string>
      custom_css?: Record<string, any>
    }
    seo?: {
      title?: string
      description?: string
      keywords?: string
      og_image?: string
    }
    contact?: Record<string, any>
    custom?: Record<string, any>
  }
  status: 'pending' | 'active' | 'failed' | 'suspended'
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface DomainConfig {
  id: string
  frontend_hostname?: string
  backend_hostname?: string
  branding: {
    name: string
    slogan?: string
    logo_url?: string
    favicon_url?: string
  }
  theme: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    text_secondary: string
  }
  seo: {
    title: string
    description?: string
    keywords?: string
    og_image?: string
  }
  contact: Record<string, any>
  custom: Record<string, any>
  custom_css: Record<string, any>
}

export interface DomainContextType {
  config: DomainConfig | null
}
